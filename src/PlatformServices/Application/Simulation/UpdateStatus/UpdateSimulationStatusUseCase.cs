using System;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Database.Repositories.Simulation;
using Domain.Enums;
using Model.Simulation.UpdateStatus.Request;
using static Application.Core.UseCaseResponseFactory<System.Guid>;

namespace Application.Simulation.UpdateStatus;

public sealed class UpdateSimulationStatusUseCase
    : IUpdateSimulationStatusUseCase
{
    private readonly ISimulationRepository _simulationRepository;

    private readonly UpdateSimulationStatusRequestValidator _requestValidator =
        new();

    public UpdateSimulationStatusUseCase(
        ISimulationRepository simulationRepository)
    {
        _simulationRepository = simulationRepository;
    }

    public async Task<UseCaseResponse<Guid>> Execute(
        UpdateSimulationStatusRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(Guid.Empty);
            }

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    Guid.Empty,
                    validationResult.Errors.ToErrorMessages());
            }

            var simulation = await _simulationRepository
                .GetAsync(request.Id);

            if (simulation is null)
            {
                return NotFound(Guid.Empty);
            }

            if (simulation.Status == SimulationStatus.Completed)
            {
                return BadRequest(
                    Guid.Empty,
                    "000",
                    "Simulation is already completed.");
            }

            var (isSuccessful, @event) = simulation.UpdateStatus(
                request.Status, 
                request.Event);

            if (!isSuccessful)
            {
                return BadRequest(
                    Guid.Empty,
                    "000",
                    "Simulation status cannot be updated when it is completed.");
            }
            
            await _simulationRepository.UpdateStatusAsync(@event);

            return Ok(request.Id);
        }
        catch (Exception e)
        {
            return InternalServerError(
                Guid.Empty,
                e.ToErrorMessages("000"));
        }
    }
}
