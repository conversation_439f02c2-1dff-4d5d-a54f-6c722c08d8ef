using Domain.Entities;

namespace Database.Repositories.Simulation.Extensions;

public static class SimulationEventExtensions
{
    public static object ToDbWriteParam(this SimulationEvent simulationEvent)
    {
        return string.IsNullOrWhiteSpace(simulationEvent.Event)
            ? null
            : new
            {
                simulationEvent.Id,
                simulationEvent.Event,
                SimulationId = simulationEvent.Simulation.Id
            };
    }
}
