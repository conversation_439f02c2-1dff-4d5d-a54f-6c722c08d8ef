using System;
using System.Collections.Generic;
using System.Linq;

namespace Database.Repositories.Simulation.Extensions;

public static class UserCollectionExtensions
{
    public static object ToDbWriteParam(
        this List<Domain.Entities.User> users,
        Guid simulationId)
    {
        return users.Select(user => new
        {
            SimulationId = simulationId,
            UserId = user.Id
        });
    }
}
