using Database.Core;
using Model.Simulation.List.Request;
using Model.Simulation.List.Response;
using Model.Simulation.Search.Request;
using Model.Simulation.Search.Response;
using Model.Simulation.UpdateStatus.Request;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Enums;
using Model.Core.Search.Pagination;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;

namespace Database.Repositories.Simulation
{
    public interface ISimulationRepository : IRepository<Domain.Entities.Simulation>
    {
        Task<PaginationResponse> SearchAsync(SearchSimulationRequest request);
        Task AddAuthorizedUsersAsync(Guid simulationId, List<Guid> users);
        Task RemoveAuthorizedUserAsync(Guid simulationId, Guid user);
        Task PatchAsync(Domain.Entities.Simulation simulation);
        Task<IEnumerable<ListSimulationResponse>> ListAsync(ListSimulationRequest request);
        Task UpdateStatusAsync(SimulationEvent @event);
        Task<GetSimulationByIdResponse> GetByIdAsync(GetSimulationByIdRequest request);
    }
}
