using Bogus;
using Domain.Entities;
using Domain.Enums;
using FluentAssertions;
using System;
using System.Linq;
using Xunit;

namespace Domain.Tests.Entities.Simulation;

public class UpdateStatusTests
{
    private readonly Faker _faker = new();
    
    private Domain.Entities.Simulation CreateSimulation(SimulationStatus status)
    {
        return new Domain.Entities.Simulation
        {
            Id = _faker.Random.Guid(),
            Name = _faker.Lorem.Word(),
            Status = status,
            CreatedDate = _faker.Date.Recent(),
            Events = new List<SimulationEvent>()
        };
    }

    [Fact(DisplayName = "Should successfully update status from Processing to Failed with valid reason")]
    public void WhenCurrentStatusIsProcessingAndNewStatusIsFailed_ShouldReturnSuccessAndCreateEvent()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (isSuccessful, _) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        isSuccessful.Should().BeTrue();
    }

    [Fact(DisplayName = "Should successfully update status from Processing to Completed with valid reason")]
    public void WhenCurrentStatusIsProcessingAndNewStatusIsCompleted_ShouldReturnSuccessAndCreateEvent()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Completed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (isSuccessful, _) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        isSuccessful.Should().BeTrue();
    }

    [Fact(DisplayName = "Should successfully update status from Failed to Processing with valid reason")]
    public void WhenCurrentStatusIsFailedAndNewStatusIsProcessing_ShouldReturnSuccessAndCreateEvent()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Failed);
        var newStatus = SimulationStatus.Processing;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (isSuccessful, _) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        isSuccessful.Should().BeTrue();
    }

    [Fact(DisplayName = "Should update simulation status to new value when update is successful")]
    public void WhenUpdateIsSuccessful_ShouldUpdateSimulationStatus()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulation.Status.Should().Be(newStatus);
    }

    [Fact(DisplayName = "Should create simulation event with correct properties when update is successful")]
    public void WhenUpdateIsSuccessful_ShouldCreateSimulationEventWithCorrectProperties()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (_, simulationEvent) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulationEvent.Should().NotBeNull();
    }

    [Fact(DisplayName = "Should set simulation reference in created event when update is successful")]
    public void WhenUpdateIsSuccessful_ShouldSetSimulationReferenceInCreatedEvent()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (_, simulationEvent) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulationEvent.Simulation.Should().BeSameAs(simulation);
    }

    [Fact(DisplayName = "Should set event reason in created event when update is successful")]
    public void WhenUpdateIsSuccessful_ShouldSetEventReasonInCreatedEvent()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (_, simulationEvent) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulationEvent.Event.Should().Be(reason);
    }

    [Fact(DisplayName = "Should add created event to simulation events collection when update is successful")]
    public void WhenUpdateIsSuccessful_ShouldAddCreatedEventToSimulationEventsCollection()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var initialEventCount = simulation.Events.Count;
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (_, _) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulation.Events.Should().HaveCount(initialEventCount + 1);
    }

    [Fact(DisplayName = "Should add the exact created event to simulation events collection when update is successful")]
    public void WhenUpdateIsSuccessful_ShouldAddExactCreatedEventToSimulationEventsCollection()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Failed;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (_, simulationEvent) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulation.Events.Should().Contain(simulationEvent);
    }
    
    [Fact(DisplayName = "Should return false when current status is Completed")]
    public void WhenCurrentStatusIsCompleted_ShouldReturnFalse()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Completed);
        var newStatus = SimulationStatus.Processing;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (isSuccessful, _) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        isSuccessful.Should().BeFalse();
    }

    [Fact(DisplayName = "Should return null event when current status is Completed")]
    public void WhenCurrentStatusIsCompleted_ShouldReturnNullEvent()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Completed);
        var newStatus = SimulationStatus.Processing;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (_, simulationEvent) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulationEvent.Should().BeNull();
    }

    [Fact(DisplayName = "Should not change simulation status when current status is Completed")]
    public void WhenCurrentStatusIsCompleted_ShouldNotChangeSimulationStatus()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Completed);
        var originalStatus = simulation.Status;
        var newStatus = SimulationStatus.Processing;
        var reason = _faker.Lorem.Sentence();

        // Act
        simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulation.Status.Should().Be(originalStatus);
    }

    [Fact(DisplayName = "Should not add any events when current status is Completed")]
    public void WhenCurrentStatusIsCompleted_ShouldNotAddAnyEvents()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Completed);
        var initialEventCount = simulation.Events.Count;
        var newStatus = SimulationStatus.Processing;
        var reason = _faker.Lorem.Sentence();

        // Act
        simulation.UpdateStatus(newStatus, reason);

        // Assert
        simulation.Events.Should().HaveCount(initialEventCount);
    }

    [Fact(DisplayName = "Should update to same status successfully")]
    public void WhenNewStatusIsSameAsCurrentStatus_ShouldHandleSuccessfully()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var newStatus = SimulationStatus.Processing;
        var reason = _faker.Lorem.Sentence();

        // Act
        var (isSuccessful, _) = simulation.UpdateStatus(newStatus, reason);

        // Assert
        isSuccessful.Should().BeTrue();
    }

    [Fact(DisplayName = "Should maintain correct final status after multiple updates")]
    public void WhenCalledMultipleTimes_ShouldMaintainCorrectFinalStatus()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var firstReason = _faker.Lorem.Sentence();
        var secondReason = _faker.Lorem.Sentence();
        var finalStatus = SimulationStatus.Failed;

        // Act
        simulation.UpdateStatus(SimulationStatus.Completed, firstReason);
        simulation.UpdateStatus(finalStatus, secondReason); // This should fail due to Completed status

        // Assert
        simulation.Status.Should().Be(SimulationStatus.Completed);
    }

    [Fact(DisplayName = "Should prevent further updates after reaching Completed status")]
    public void WhenStatusIsCompletedAfterUpdate_ShouldPreventFurtherUpdates()
    {
        // Arrange
        var simulation = CreateSimulation(SimulationStatus.Processing);
        var firstReason = _faker.Lorem.Sentence();
        var secondReason = _faker.Lorem.Sentence();

        // Act
        simulation.UpdateStatus(SimulationStatus.Completed, firstReason);
        var (secondSuccess, _) = simulation.UpdateStatus(SimulationStatus.Failed, secondReason);

        // Assert
        secondSuccess.Should().BeFalse();
    }
}
